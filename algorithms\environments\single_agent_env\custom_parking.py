# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    保持父类的基础功能，只添加摄像头固定中央的功能和观察空间扁平化
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境"""
        # 重置两阶段停车相关的状态
        if hasattr(self, 'parking_stage'):
            delattr(self, 'parking_stage')
        if hasattr(self, 'last_distance_to_front'):
            delattr(self, 'last_distance_to_front')
        if hasattr(self, 'last_distance_to_target'):
            delattr(self, 'last_distance_to_target')
        if hasattr(self, 'stage_1_completed'):
            delattr(self, 'stage_1_completed')

        # 重置旧的探索相关状态（保持兼容性）
        if hasattr(self, 'last_distance'):
            delattr(self, 'last_distance')

        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)
        obs = self._flatten_observation(obs)
        return obs, info

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """两阶段停车奖励函数：先到前方区域，再倒车入库"""

        # 1. 保留基础的目标导向奖励（距离奖励 + 碰撞惩罚）
        base_reward = super()._reward(action)

        # 2. 添加两阶段停车引导奖励
        stage_reward = self._get_two_stage_parking_reward()

        # 3. 轻微的效率压力（避免无限磨蹭）
        efficiency_penalty = -0.01

        return base_reward + stage_reward + efficiency_penalty

    def _get_two_stage_parking_reward(self) -> float:
        """两阶段停车奖励：第一阶段到前方区域，第二阶段倒车入库"""
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position
        vehicle_heading = self.vehicle.heading
        target_heading = self.vehicle.goal.heading

        # 计算车辆相对于目标的位置关系
        relative_pos = vehicle_pos - target_pos

        # 计算目标前方区域的位置（目标位置前方6-8米的区域）
        target_front_direction = np.array([np.cos(target_heading), np.sin(target_heading)])
        front_area_center = target_pos + target_front_direction * 7.0  # 前方7米处

        # 计算到目标和前方区域的距离
        distance_to_target = np.linalg.norm(relative_pos)
        distance_to_front_area = np.linalg.norm(vehicle_pos - front_area_center)

        # 初始化状态跟踪
        if not hasattr(self, 'parking_stage'):
            self.parking_stage = 1  # 1: 前往前方区域, 2: 倒车入库
            self.last_distance_to_front = distance_to_front_area
            self.last_distance_to_target = distance_to_target
            self.stage_1_completed = False

        reward = 0.0

        # 判断是否在前方区域（距离前方区域中心3米以内，且朝向大致正确）
        in_front_area = distance_to_front_area < 3.0
        heading_aligned = abs(vehicle_heading - target_heading) < np.pi/3  # 60度以内

        # 阶段1：前往前方区域
        if self.parking_stage == 1:
            # 奖励接近前方区域
            if distance_to_front_area < self.last_distance_to_front:
                reward += 0.8  # 接近前方区域的奖励

            # 奖励正确的朝向调整
            if heading_aligned:
                reward += 0.3  # 朝向正确的奖励

            # 检查是否完成第一阶段
            if in_front_area and heading_aligned:
                self.parking_stage = 2
                self.stage_1_completed = True
                reward += 2.0  # 完成第一阶段的大奖励
                print(f"阶段1完成！车辆到达前方区域，切换到倒车入库阶段")

        # 阶段2：倒车入库
        elif self.parking_stage == 2:
            # 计算车辆的前进速度（正值为前进，负值为倒车）
            velocity = self.vehicle.velocity
            forward_speed = velocity[0] * np.cos(vehicle_heading) + velocity[1] * np.sin(vehicle_heading)

            # 奖励倒车行为
            if forward_speed < -0.1:  # 正在倒车
                reward += 0.5

                # 如果倒车时接近目标，给额外奖励
                if distance_to_target < self.last_distance_to_target:
                    reward += 1.0  # 倒车接近目标的高奖励

            # 惩罚在第二阶段前进（除非是微调）
            elif forward_speed > 0.2:
                reward -= 0.3

            # 奖励朝向调整（在倒车过程中）
            heading_diff = abs(vehicle_heading - target_heading)
            heading_diff = min(heading_diff, 2*np.pi - heading_diff)
            if heading_diff < np.pi/6:  # 30度以内
                reward += 0.4

        # 更新距离记录
        self.last_distance_to_front = distance_to_front_area
        self.last_distance_to_target = distance_to_target

        return reward



    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """判断是否成功完成两阶段停车"""
        # 1. 基础位置要求
        base_reward = super().compute_reward(achieved_goal, desired_goal, {})
        position_ok = base_reward > -0.15  # 稍微放宽位置要求

        if not position_ok:
            return False

        # 2. 车身角度要求（与目标朝向接近）
        vehicle_heading = self.vehicle.heading
        target_heading = self.vehicle.goal.heading
        heading_diff = abs(vehicle_heading - target_heading)
        heading_diff = min(heading_diff, 2*np.pi - heading_diff)  # 取较小角度
        heading_ok = heading_diff < np.pi/4  # 45度以内

        # 3. 速度要求（要停下来）
        speed = np.linalg.norm(self.vehicle.velocity)
        stopped = speed < 1.0

        # 4. 确保完成了两阶段停车过程
        completed_two_stage = getattr(self, 'stage_1_completed', False)

        return heading_ok and stopped and completed_two_stage

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result

    def get_parking_info(self) -> dict:
        """获取停车状态信息，用于调试和监控"""
        if not hasattr(self, 'parking_stage'):
            return {"stage": "未初始化"}

        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position
        vehicle_heading = self.vehicle.heading
        target_heading = self.vehicle.goal.heading

        # 计算前方区域位置
        target_front_direction = np.array([np.cos(target_heading), np.sin(target_heading)])
        front_area_center = target_pos + target_front_direction * 7.0

        # 计算距离
        distance_to_target = np.linalg.norm(vehicle_pos - target_pos)
        distance_to_front_area = np.linalg.norm(vehicle_pos - front_area_center)

        # 计算速度
        velocity = self.vehicle.velocity
        forward_speed = velocity[0] * np.cos(vehicle_heading) + velocity[1] * np.sin(vehicle_heading)

        # 计算朝向差异
        heading_diff = abs(vehicle_heading - target_heading)
        heading_diff = min(heading_diff, 2*np.pi - heading_diff)

        return {
            "当前阶段": "前往前方区域" if self.parking_stage == 1 else "倒车入库",
            "阶段1是否完成": getattr(self, 'stage_1_completed', False),
            "到目标距离": f"{distance_to_target:.2f}m",
            "到前方区域距离": f"{distance_to_front_area:.2f}m",
            "前进速度": f"{forward_speed:.2f}m/s",
            "朝向差异": f"{np.degrees(heading_diff):.1f}°",
            "车辆位置": f"({vehicle_pos[0]:.1f}, {vehicle_pos[1]:.1f})",
            "目标位置": f"({target_pos[0]:.1f}, {target_pos[1]:.1f})",
            "前方区域中心": f"({front_area_center[0]:.1f}, {front_area_center[1]:.1f})"
        }


def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
