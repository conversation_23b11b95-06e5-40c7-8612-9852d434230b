# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    保持父类的基础功能，只添加摄像头固定中央的功能和观察空间扁平化
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境"""
        # 重置探索相关的状态
        if hasattr(self, 'last_distance'):
            delattr(self, 'last_distance')

        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)
        obs = self._flatten_observation(obs)
        return obs, info

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """让智能体自己探索停车策略的奖励函数"""

        # 1. 保留基础的目标导向奖励（距离奖励 + 碰撞惩罚）
        base_reward = super()._reward(action)

        # 2. 添加最少的探索引导
        exploration_bonus = self._get_exploration_bonus()

        # 3. 添加平滑驾驶奖励（解决高速转向问题）
        smooth_driving_bonus = self._get_smooth_driving_bonus(action)

        # 4. 轻微的效率压力（避免无限磨蹭）
        efficiency_penalty = -0.01

        return base_reward + exploration_bonus + smooth_driving_bonus + efficiency_penalty

    def _get_exploration_bonus(self) -> float:
        """鼓励探索的奖励"""
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position

        # 计算当前距离
        current_distance = np.linalg.norm(vehicle_pos - target_pos)

        # 进步奖励：只要比上一步更接近目标就给奖励
        if not hasattr(self, 'last_distance'):
            self.last_distance = current_distance
            return 0.0

        if current_distance < self.last_distance:
            progress_reward = 0.5  # 进步奖励
        else:
            progress_reward = 0.0  # 不惩罚退步，允许探索

        self.last_distance = current_distance
        return progress_reward

    def _get_smooth_driving_bonus(self, action: np.ndarray) -> float:
        """平衡的驾驶奖励：有引导但不过度规则化"""
        speed = np.linalg.norm(self.vehicle.velocity)
        steering_action = abs(action[1]) if len(action) > 1 else 0

        # 1. 宽松的正向引导（告诉它大致方向）
        positive_guidance = 0.0
        if 0.5 < speed < 12.0:  # 很宽的合理范围
            positive_guidance = 0.05  # 轻微奖励

        # 2. 明确的危险惩罚（告诉它绝对不能做的）
        danger_penalty = 0.0
        if speed > 15.0:  # 真正危险的速度
            danger_penalty = -0.3
        if speed > 10.0 and steering_action > 0.6:  # 真正危险的高速转向
            danger_penalty -= 0.2

        # 3. 轻微的平滑性提示（鼓励但不强制）
        smoothness_hint = -(steering_action ** 1.5) * 0.08  # 温和的平滑性引导

        return positive_guidance + danger_penalty + smoothness_hint

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """判断是否成功达到目标 - 使用更合理的成功标准"""
        # 计算原始的目标距离奖励
        base_reward = super().compute_reward(achieved_goal, desired_goal, {})

        # 使用更合理的成功标准
        return base_reward > -0.2

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result



def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
