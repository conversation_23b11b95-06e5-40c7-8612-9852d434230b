#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两阶段停车奖励函数测试脚本

测试新设计的两阶段停车奖励函数：
1. 第一阶段：开车到车位前方空旷区域
2. 第二阶段：倒车入库

使用方法：
python test_two_stage_parking.py
"""

import numpy as np
import matplotlib.pyplot as plt
from argparse import Namespace
import sys
import os

# 添加项目路径
sys.path.append('.')
sys.path.append('./algorithms')
sys.path.append('./HighwayEnv')

from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv


def create_test_config():
    """创建测试配置"""
    config = Namespace()
    config.env_id = "parking-v0"
    config.env_seed = 42
    config.render_mode = 'human'  # 可视化模式
    config.max_episode_steps = 200
    return config


def test_manual_control():
    """手动控制测试，观察奖励函数行为"""
    print("=== 两阶段停车奖励函数测试 ===")
    print("控制说明：")
    print("- W/S: 加速/减速")
    print("- A/D: 左转/右转")
    print("- Q: 退出")
    print("- 空格: 重置环境")
    
    config = create_test_config()
    env = CustomParkingEnv(config)
    
    obs, info = env.reset()
    total_reward = 0
    step_count = 0
    
    try:
        while True:
            # 渲染环境
            env.render()
            
            # 获取停车状态信息
            parking_info = env.get_parking_info()
            print(f"\n步数: {step_count}")
            print("停车状态信息:")
            for key, value in parking_info.items():
                print(f"  {key}: {value}")
            
            # 简单的随机动作（实际使用时可以改为键盘输入）
            action = env.action_space.sample()
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            step_count += 1
            
            print(f"动作: [{action[0]:.2f}, {action[1]:.2f}]")
            print(f"奖励: {reward:.3f}, 累计奖励: {total_reward:.3f}")
            print(f"成功: {info.get('is_success', False)}")
            
            if terminated or truncated:
                print(f"\n回合结束！")
                print(f"总步数: {step_count}")
                print(f"总奖励: {total_reward:.3f}")
                print(f"是否成功: {info.get('is_success', False)}")
                
                # 重置环境
                obs, info = env.reset()
                total_reward = 0
                step_count = 0
                print("\n环境已重置，开始新回合...")
            
            # 简单的暂停，便于观察
            import time
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n测试结束")
    finally:
        env.close()


def test_reward_function():
    """测试奖励函数的数值特性"""
    print("=== 奖励函数数值测试 ===")
    
    config = create_test_config()
    config.render_mode = 'rgb_array'  # 不显示窗口
    env = CustomParkingEnv(config)
    
    # 运行多个回合，收集奖励数据
    episode_rewards = []
    episode_lengths = []
    success_count = 0
    
    for episode in range(10):
        obs, info = env.reset()
        total_reward = 0
        step_count = 0
        
        for step in range(200):  # 最大200步
            action = env.action_space.sample()  # 随机动作
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            step_count += 1
            
            if terminated or truncated:
                break
        
        episode_rewards.append(total_reward)
        episode_lengths.append(step_count)
        
        if info.get('is_success', False):
            success_count += 1
        
        print(f"回合 {episode+1}: 奖励={total_reward:.2f}, 步数={step_count}, 成功={info.get('is_success', False)}")
    
    # 统计结果
    print(f"\n=== 测试结果统计 ===")
    print(f"平均奖励: {np.mean(episode_rewards):.2f} ± {np.std(episode_rewards):.2f}")
    print(f"平均步数: {np.mean(episode_lengths):.1f} ± {np.std(episode_lengths):.1f}")
    print(f"成功率: {success_count}/10 = {success_count*10}%")
    print(f"最高奖励: {np.max(episode_rewards):.2f}")
    print(f"最低奖励: {np.min(episode_rewards):.2f}")
    
    env.close()


def test_stage_transition():
    """测试阶段转换逻辑"""
    print("=== 阶段转换测试 ===")
    
    config = create_test_config()
    config.render_mode = 'rgb_array'
    env = CustomParkingEnv(config)
    
    obs, info = env.reset()
    
    # 模拟一些动作，观察阶段转换
    for step in range(50):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        parking_info = env.get_parking_info()
        
        if step % 10 == 0:  # 每10步打印一次状态
            print(f"\n步数 {step}:")
            print(f"  当前阶段: {parking_info['当前阶段']}")
            print(f"  阶段1完成: {parking_info['阶段1是否完成']}")
            print(f"  到前方区域距离: {parking_info['到前方区域距离']}")
            print(f"  到目标距离: {parking_info['到目标距离']}")
            print(f"  奖励: {reward:.3f}")
        
        if terminated or truncated:
            print(f"\n回合在第{step}步结束")
            break
    
    env.close()


if __name__ == "__main__":
    print("选择测试模式：")
    print("1. 手动控制测试（可视化）")
    print("2. 奖励函数数值测试")
    print("3. 阶段转换测试")
    
    try:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            test_manual_control()
        elif choice == "2":
            test_reward_function()
        elif choice == "3":
            test_stage_transition()
        else:
            print("无效选择，运行默认测试...")
            test_reward_function()
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
